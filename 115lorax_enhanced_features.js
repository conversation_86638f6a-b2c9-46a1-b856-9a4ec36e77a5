// ==UserScript==
// @name         115Lorax增强功能模块
// <AUTHOR> (基于zxf10608原作)
// @version      1.1
// @description  从115lorax剥离的增强功能：Mac VLC播放器支持、DPlayer播放列表信息打印、演员信息自动标签等
// @require      https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js
// @require      https://cdn.jsdelivr.net/npm/toastr@2.1.4/toastr.min.js
// @resource     toastrCss   https://cdn.jsdelivr.net/npm/toastr@2.1.4/build/toastr.min.css
// @require      https://cdn.jsdelivr.net/npm/dplayer@1.26.0/dist/DPlayer.min.js
// @resource     dplayerCss  https://cdn.jsdelivr.net/npm/dplayer/dist/DPlayer.min.css
// @include      http*://*.115.com/*
// @exclude      http*://*.115.com/bridge*
// @exclude      http*://*.115.com/*/static*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_openInTab
// @grant        GM_setClipboard
// @grant        GM_getResourceText
// @grant        GM_xmlhttpRequest
// @grant        GM_registerMenuCommand
// @connect      115.com
// @grant        unsafeWindow
// @run-at       document-start
// @compatible   chrome
// @license      GPL License
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('115Lorax增强功能模块已加载');
    
    // 配置对象
    var Config = {
        enableVLC: GM_getValue('enableVLC', true),
        enableDPlayerDebug: GM_getValue('enableDPlayerDebug', true),
        enableActorLabels: GM_getValue('enableActorLabels', true),
        defaultPlayer: GM_getValue('defaultPlayer', 'Dplayer')
    };
    
    // 注册菜单命令
    GM_registerMenuCommand('VLC播放器开关', function() {
        Config.enableVLC = !Config.enableVLC;
        GM_setValue('enableVLC', Config.enableVLC);
        alert('VLC播放器支持已' + (Config.enableVLC ? '开启' : '关闭'));
        location.reload();
    });
    
    GM_registerMenuCommand('DPlayer调试开关', function() {
        Config.enableDPlayerDebug = !Config.enableDPlayerDebug;
        GM_setValue('enableDPlayerDebug', Config.enableDPlayerDebug);
        alert('DPlayer调试信息已' + (Config.enableDPlayerDebug ? '开启' : '关闭'));
    });

    GM_registerMenuCommand('演员标签开关', function() {
        Config.enableActorLabels = !Config.enableActorLabels;
        GM_setValue('enableActorLabels', Config.enableActorLabels);
        alert('演员标签功能已' + (Config.enableActorLabels ? '开启' : '关闭'));
    });
    
    // 初始化通知系统
    function initNotification() {
        GM_addStyle(GM_getResourceText('toastrCss'));
        GM_addStyle('.toast{font-size:15px!important;width:360px!important;} .toast-title{font-size:16px!important;text-align:center}');
        
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "progressBar": true,
            "timeOut": 8000,
            "extendedTimeOut": 8000,
            "positionClass": 'toast-top-right',
            "allowHtml": true,
            "newestOnTop": false,
        };
    }
    
    // Mac VLC播放器调用函数
    function callMacVLC(videoUrl, videoName) {
        console.log('调用Mac VLC播放器:', videoUrl);
        
        // 方法1: 尝试vlc协议
        try {
            window.location.href = 'vlc://' + encodeURIComponent(videoUrl);
            toastr.success('正在调用VLC播放器...', 'VLC播放器');
        } catch(e) {
            // 方法2: 复制链接到剪贴板并提示用户
            GM_setClipboard(videoUrl);
            var helpText = `VLC协议未注册，视频地址已复制到剪贴板。请：
1. 打开VLC播放器
2. 点击菜单 文件 > 打开网络串流 (⌘+N)
3. 粘贴地址并播放

或者手动注册VLC协议：
打开终端执行: /Applications/VLC.app/Contents/MacOS/VLC --intf=rc`;
            
            toastr.info(helpText, 'VLC播放器调用', {timeOut: 15000});
            console.log('VLC协议调用失败，已复制链接到剪贴板');
        }
    }
    
    // DPlayer播放列表信息打印函数
    function printDPlayerInfo(m3u8List, videoName) {
        if (!Config.enableDPlayerDebug) return;
        
        console.log('=== DPlayer M3U8 播放列表 ===');
        console.log('视频名称:', videoName);
        console.log('m3u8列表:', m3u8List);
        
        if (m3u8List && m3u8List.length > 0) {
            m3u8List.forEach((item, index) => {
                console.log(`清晰度 ${index + 1}: ${item.name} - ${item.url}`);
            });
        }
        console.log('=============================');
    }
    
    // 获取视频M3U8播放列表
    function getM3u8(pickCode) {
        return new Promise(function(resolve, reject) {
            var url = 'https://115.com/api/video/m3u8/' + pickCode + '.m3u8';
            
            GM_xmlhttpRequest({
                method: "GET",
                url: url,
                headers: {
                    "User-Agent": navigator.userAgent,
                    Origin: "https://115.com",
                },
                onload: function(response) {
                    if (response.status === 200) {
                        var htmlTxt = response.responseText;
                        if (typeof htmlTxt === 'undefined') {
                            resolve([false, null]);
                            return;
                        }
                        
                        var dataList = htmlTxt.split('\n');
                        var m3u8 = [];
                        var temp = '"YH"|原画|"BD"|4K|"UD"|蓝光|"HD"|超清|"SD"|高清|"3G"|标清';
                        var txt = temp.split('|');
                        
                        for (var i = 0; i < 6; i++) {
                            dataList.forEach(function (e, j, arr) {
                                if (e.indexOf(txt[i * 2]) != -1) {
                                    m3u8.push({
                                        name: txt[i * 2 + 1], 
                                        url: arr[j + 1].replace(/\r/g, ''), 
                                        type: 'hls'
                                    });
                                }
                            });
                        }
                        
                        resolve([true, m3u8]);
                    } else {
                        resolve([false, null]);
                    }
                },
                onerror: function(error) {
                    resolve([false, null]);
                }
            });
        });
    }
    
    // 播放器选择和调用函数
    function playVideo(pickCode, videoName, playerType) {
        playerType = playerType || Config.defaultPlayer;
        
        if (playerType === 'VLC' && Config.enableVLC) {
            // 获取M3U8播放列表
            getM3u8(pickCode).then(function(result) {
                if (result[0] && result[1].length > 0) {
                    var m3u8List = result[1];
                    printDPlayerInfo(m3u8List, videoName);
                    
                    // 选择最高清晰度
                    var videoUrl = m3u8List[0].url;
                    callMacVLC(videoUrl, videoName);
                } else {
                    toastr.error('无法获取视频播放地址', '播放失败');
                }
            });
        } else if (playerType === 'Dplayer') {
            // 调用DPlayer播放
            getM3u8(pickCode).then(function(result) {
                if (result[0] && result[1].length > 0) {
                    var m3u8List = result[1];
                    printDPlayerInfo(m3u8List, videoName);
                    
                    // 打开DPlayer播放页面
                    var playUrl = 'https://115.com/?ct=play&ac=location&pickcode=' + pickCode + '&hls=1';
                    GM_openInTab(playUrl, false);
                } else {
                    toastr.error('无法获取视频播放地址', '播放失败');
                }
            });
        }
    }
    
    // 测试函数
    function testFeatures() {
        console.log('=== 115Lorax增强功能测试 ===');

        // 测试VLC调用
        if (Config.enableVLC) {
            console.log('VLC播放器支持: 已启用');
            // callMacVLC('http://test.url/video.mp4', '测试视频');
        } else {
            console.log('VLC播放器支持: 已禁用');
        }

        // 测试DPlayer调试信息
        if (Config.enableDPlayerDebug) {
            console.log('DPlayer调试信息: 已启用');
            var testM3u8 = [
                {name: '超清', url: 'http://test.url/hd.m3u8', type: 'hls'},
                {name: '高清', url: 'http://test.url/sd.m3u8', type: 'hls'}
            ];
            printDPlayerInfo(testM3u8, '测试视频');
        } else {
            console.log('DPlayer调试信息: 已禁用');
        }

        // 测试演员标签功能
        if (Config.enableActorLabels) {
            console.log('演员标签功能: 已启用');
            console.log('可用方法: getActorNames, getId, addLabel, editFile, processActorLabels');
        } else {
            console.log('演员标签功能: 已禁用');
        }

        console.log('配置信息:', Config);
        console.log('=============================');
    }
    
    // 页面加载完成后初始化
    $(document).ready(function() {
        if (window.location.href.indexOf('115.com') !== -1) {
            initNotification();
            
            // 添加增强功能到115网盘界面
            if (window.location.href.indexOf('115.com/?ct=file') !== -1) {
                addEnhancedFeatures();
            }
            
            // 运行测试
            setTimeout(testFeatures, 1000);
        }
    });
    
    // 添加增强功能到115网盘界面
    function addEnhancedFeatures() {
        // 监听文件列表的鼠标悬停事件
        $('body').on('mouseenter', 'li[file_type="1"]:has(.duration):not([enhanced_added="1"])', function() {
            var $el = $(this).attr('enhanced_added', '1');
            var pickCode = $el.attr('pick_code');
            var fileName = $el.attr('title');
            
            if (pickCode && fileName) {
                // 添加VLC播放按钮
                if (Config.enableVLC) {
                    $el.find('.file-opr').prepend(
                        '<a href="javascript:;" class="vlc-play" title="使用VLC播放视频">' +
                        '<span>VLC播放</span></a>'
                    );
                }
                
                // 添加DPlayer调试按钮
                if (Config.enableDPlayerDebug) {
                    $el.find('.file-opr').prepend(
                        '<a href="javascript:;" class="dplayer-debug" title="DPlayer调试播放">' +
                        '<span>调试播放</span></a>'
                    );
                }
            }
        });
        
        // VLC播放按钮点击事件
        $('body').on('click', '.vlc-play', function(e) {
            e.preventDefault();
            var $li = $(this).closest('li[file_type="1"]');
            var pickCode = $li.attr('pick_code');
            var fileName = $li.attr('title');
            
            if (pickCode && fileName) {
                playVideo(pickCode, fileName, 'VLC');
            }
        });
        
        // DPlayer调试播放按钮点击事件
        $('body').on('click', '.dplayer-debug', function(e) {
            e.preventDefault();
            var $li = $(this).closest('li[file_type="1"]');
            var pickCode = $li.attr('pick_code');
            var fileName = $li.attr('title');
            
            if (pickCode && fileName) {
                playVideo(pickCode, fileName, 'Dplayer');
            }
        });
    }
    
    // 演员信息功能模块
    var ActorLabels = {
        // 从页面提取演员名称
        getActorNames: function() {
            console.log('🎭 开始提取演员信息...');

            // 选择所有包含演员信息的链接
            const actorLinks = document.querySelectorAll('.panel-block .value a[href*="actors"]');
            const actorNames = [];

            // 遍历每个链接提取演员名称
            actorLinks.forEach((link) => {
                // 检查下一个兄弟元素是否为女性演员标识
                const nextSibling = link.nextElementSibling;
                if (nextSibling && nextSibling.tagName === "STRONG" &&
                    nextSibling.classList.contains("symbol") &&
                    nextSibling.classList.contains("female")) {
                    actorNames.push(link.textContent.trim());
                }
            });

            console.log('🎭 提取到的演员名称:', actorNames);
            return actorNames;
        },

        // 获取标签ID
        getId: function(actorName) {
            return new Promise(function(resolve, reject) {
                console.log('🔍 查找演员标签ID:', actorName);

                GM_xmlhttpRequest({
                    method: 'GET',
                    url: "https://webapi.115.com/label/list?keyword=" + encodeURIComponent(actorName) + "&limit=11150",
                    responseType: 'json',
                    headers: {
                        "User-Agent": navigator.userAgent,
                        "Accept": "application/json, text/javascript, */*; q=0.01",
                        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                        Origin: "https://115.com",
                        "X-Requested-With": "XMLHttpRequest"
                    },
                    onload: function(response) {
                        try {
                            var result = response.response || JSON.parse(response.responseText);
                            if (result.state && result.data && result.data.length > 0) {
                                // 查找完全匹配的标签
                                var exactMatch = result.data.find(function(item) {
                                    return item.name === actorName;
                                });

                                if (exactMatch) {
                                    console.log('✅ 找到演员标签ID:', actorName, '->', exactMatch.id);
                                    resolve(exactMatch.id);
                                } else {
                                    console.log('❌ 未找到完全匹配的标签:', actorName);
                                    reject(new Error('标签不存在'));
                                }
                            } else {
                                console.log('❌ 标签查询失败或无结果:', actorName);
                                reject(new Error('标签查询失败'));
                            }
                        } catch (e) {
                            console.error('❌ 解析标签查询结果失败:', e);
                            reject(e);
                        }
                    },
                    onerror: function(error) {
                        console.error('❌ 标签查询请求失败:', error);
                        reject(error);
                    }
                });
            });
        },

        // 添加新标签
        addLabel: function(labelName) {
            return new Promise(function(resolve, reject) {
                console.log('➕ 创建新演员标签:', labelName);

                var requestBody = "name%5B%5D=" + encodeURIComponent(labelName) + "%07%23000000";

                GM_xmlhttpRequest({
                    method: 'POST',
                    url: "https://webapi.115.com/label/add_multi",
                    data: requestBody,
                    headers: {
                        "User-Agent": navigator.userAgent,
                        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                        "Accept": "application/json, text/javascript, */*; q=0.01",
                        Origin: "https://115.com",
                        "X-Requested-With": "XMLHttpRequest"
                    },
                    onload: function(response) {
                        try {
                            var result = JSON.parse(response.responseText);
                            if (result.state) {
                                console.log('✅ 演员标签创建成功:', labelName);
                                resolve(result);
                            } else {
                                console.error('❌ 演员标签创建失败:', result);
                                reject(result);
                            }
                        } catch (e) {
                            console.error('❌ 解析标签创建结果失败:', e);
                            reject(e);
                        }
                    },
                    onerror: function(error) {
                        console.error('❌ 标签创建请求失败:', error);
                        reject(error);
                    }
                });
            });
        },

        // 为文件添加标签
        editFile: function(fileId, labelIds) {
            return new Promise(function(resolve, reject) {
                console.log('🏷️ 为文件添加标签:', fileId, '->', labelIds);

                var requestBody = "fid=" + encodeURIComponent(fileId) + "&file_label=" + encodeURIComponent(labelIds);

                GM_xmlhttpRequest({
                    method: 'POST',
                    url: "https://webapi.115.com/files/edit",
                    data: requestBody,
                    headers: {
                        "User-Agent": navigator.userAgent,
                        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                        "Accept": "application/json, text/javascript, */*; q=0.01",
                        Origin: "https://115.com",
                        "X-Requested-With": "XMLHttpRequest"
                    },
                    onload: function(response) {
                        try {
                            var result = JSON.parse(response.responseText);
                            if (result.state) {
                                console.log('✅ 文件标签添加成功');
                                resolve(result);
                            } else {
                                console.error('❌ 文件标签添加失败:', result);
                                reject(result);
                            }
                        } catch (e) {
                            console.error('❌ 解析文件编辑结果失败:', e);
                            reject(e);
                        }
                    },
                    onerror: function(error) {
                        console.error('❌ 文件编辑请求失败:', error);
                        reject(error);
                    }
                });
            });
        },

        // 处理演员标签的完整流程
        processActorLabels: function(fileId) {
            if (!Config.enableActorLabels) {
                console.log('🔇 演员标签功能已禁用');
                return Promise.resolve();
            }

            console.log('🎬 开始处理演员标签流程...');

            var actorNames = this.getActorNames();
            if (actorNames.length === 0) {
                console.log('ℹ️ 未找到演员信息');
                return Promise.resolve();
            }

            var self = this;
            var promises = actorNames.map(function(actorName) {
                return self.getId(actorName).then(function(id) {
                    console.log('✅ 获取到演员标签ID:', actorName, '->', id);
                    return id;
                }).catch(function(error) {
                    console.log('⚠️ 演员标签不存在，尝试创建:', actorName);
                    return self.addLabel(actorName).then(function(result) {
                        console.log('✅ 演员标签创建成功:', actorName);
                        return self.getId(actorName);
                    });
                });
            });

            return Promise.all(promises).then(function(ids) {
                var validIds = ids.filter(function(id) { return id; });
                if (validIds.length > 0) {
                    var labelIds = validIds.join(',');
                    console.log('🏷️ 准备添加标签到文件:', fileId, '标签IDs:', labelIds);

                    return self.editFile(fileId, labelIds).then(function(result) {
                        console.log('🎉 演员标签处理完成!');
                        toastr.success('已为文件添加 ' + actorNames.length + ' 个演员标签', '演员标签处理完成');
                        return result;
                    });
                } else {
                    console.log('⚠️ 没有有效的标签ID');
                    return Promise.resolve();
                }
            }).catch(function(error) {
                console.error('❌ 演员标签处理失败:', error);
                toastr.error('演员标签处理失败: ' + error.message, '处理失败');
                throw error;
            });
        }
    };

    // 暴露API供外部调用
    unsafeWindow.LoraxEnhanced = {
        playVideo: playVideo,
        callMacVLC: callMacVLC,
        printDPlayerInfo: printDPlayerInfo,
        testFeatures: testFeatures,
        actorLabels: ActorLabels,
        config: Config
    };

    console.log('115Lorax增强功能模块初始化完成');
})();
