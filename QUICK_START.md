# 115Lorax增强功能模块 - 快速开始

## 🚀 5分钟快速上手

### 第1步：安装脚本 (2分钟)
1. 安装 [Tampermonkey](https://tampermonkey.net/) 浏览器扩展
2. 复制 `115lorax_enhanced_features.js` 的全部内容
3. 在Tampermonkey中创建新脚本并粘贴
4. 保存并启用脚本

### 第2步：验证安装 (1分钟)
1. 打开 `test_enhanced_features.html` 测试页面
2. 点击"检查脚本状态"按钮
3. 确认看到绿色的成功提示

### 第3步：开始使用 (2分钟)
1. 访问 [115网盘](https://115.com)
2. 在视频文件上看到新的播放按钮
3. 在有演员信息的页面自动处理标签

## 🎯 核心功能速览

### Mac VLC播放器 🎥
- **触发方式**: 点击"VLC播放"按钮
- **自动功能**: 调用VLC播放器或复制链接
- **适用场景**: Mac用户播放115网盘视频

### DPlayer调试信息 🐛
- **触发方式**: 播放视频时自动输出
- **查看位置**: 浏览器控制台(F12)
- **显示内容**: M3U8播放列表详细信息

### 演员标签处理 🎭
- **触发方式**: 调用API或自动检测
- **处理流程**: 提取演员→创建标签→应用到文件
- **适用场景**: 自动为下载的资源添加演员标签

## 📱 快速API调用

```javascript
// 在浏览器控制台中直接调用

// 1. 使用VLC播放视频
LoraxEnhanced.playVideo('pickcode123', '电影名.mp4', 'VLC');

// 2. 处理当前页面的演员标签
LoraxEnhanced.actorLabels.processActorLabels('file_id_123');

// 3. 查看当前配置
console.log(LoraxEnhanced.config);

// 4. 运行功能测试
LoraxEnhanced.testFeatures();
```

## ⚙️ 快速配置

### 通过菜单配置
1. 点击Tampermonkey图标
2. 选择对应的开关选项：
   - "VLC播放器开关"
   - "DPlayer调试开关" 
   - "演员标签开关"

### 通过代码配置
```javascript
// 启用所有功能
LoraxEnhanced.config.enableVLC = true;
LoraxEnhanced.config.enableDPlayerDebug = true;
LoraxEnhanced.config.enableActorLabels = true;
```

## 🧪 快速测试

### 使用测试页面
1. 打开 `test_enhanced_features.html`
2. 点击"运行所有测试"
3. 查看测试结果

### 使用演员标签专项测试
1. 运行 `node test_actor_labels.js`
2. 查看控制台输出
3. 验证所有功能正常

## 🔧 常见问题快速解决

### VLC无法调用？
```bash
# 检查VLC是否安装
ls /Applications/VLC.app

# 手动注册VLC协议
/Applications/VLC.app/Contents/MacOS/VLC --intf=rc
```

### 脚本未加载？
1. 检查Tampermonkey是否启用
2. 确认脚本匹配规则正确
3. 刷新页面重新加载

### 演员标签不工作？
1. 确认在正确的页面（包含演员信息）
2. 检查网络连接和115登录状态
3. 查看控制台错误信息

## 📚 更多资源

- **详细文档**: `README.md`
- **安装指南**: `INSTALL.md`
- **项目总结**: `SUMMARY.md`
- **功能演示**: `demo.js`

## 🎉 开始享受增强功能！

现在您已经完成了115Lorax增强功能模块的快速设置，可以开始享受以下便利：

✅ **一键VLC播放** - 直接调用本地播放器  
✅ **智能调试信息** - 详细的播放列表分析  
✅ **自动演员标签** - 智能文件标签管理  
✅ **无缝115集成** - 完美融入现有界面  

如果遇到任何问题，请参考详细文档或查看测试页面的故障排除部分。

---

**提示**: 这个脚本是从完整的115lorax中剥离出来的核心增强功能，专注于提供最实用的功能。如果您需要更多115网盘优化功能，可以考虑使用完整版本。
