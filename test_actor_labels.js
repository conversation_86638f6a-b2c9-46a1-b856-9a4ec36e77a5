/**
 * 演员标签功能测试脚本
 * 这个脚本专门测试从115lorax剥离出来的演员标签功能
 */

// 模拟浏览器环境
const mockStorage = {};
const mockEnvironment = {
    // 模拟GM函数
    GM_setValue: (key, value) => mockStorage[key] = value,
    GM_getValue: (key, defaultValue) => {
        return mockStorage.hasOwnProperty(key) ? mockStorage[key] : defaultValue;
    },
    GM_setClipboard: (text) => {
        console.log('📋 已复制到剪贴板:', text);
    },
    GM_xmlhttpRequest: (options) => {
        console.log('🌐 模拟HTTP请求:', options.method, options.url);
        
        // 模拟不同的API响应
        setTimeout(() => {
            if (options.url.includes('/label/list')) {
                // 模拟标签查询响应
                const keyword = decodeURIComponent(options.url.split('keyword=')[1].split('&')[0]);
                console.log('🔍 查询标签:', keyword);
                
                if (Math.random() > 0.5) {
                    // 50%概率找到标签
                    options.onload({
                        response: {
                            state: true,
                            data: [{
                                id: 'label_' + Math.floor(Math.random() * 1000),
                                name: keyword
                            }]
                        },
                        responseText: JSON.stringify({
                            state: true,
                            data: [{
                                id: 'label_' + Math.floor(Math.random() * 1000),
                                name: keyword
                            }]
                        })
                    });
                } else {
                    // 50%概率未找到标签
                    options.onload({
                        response: {
                            state: true,
                            data: []
                        },
                        responseText: JSON.stringify({
                            state: true,
                            data: []
                        })
                    });
                }
            } else if (options.url.includes('/label/add_multi')) {
                // 模拟标签创建响应
                console.log('➕ 创建标签');
                options.onload({
                    responseText: JSON.stringify({
                        state: true,
                        message: '标签创建成功'
                    })
                });
            } else if (options.url.includes('/files/edit')) {
                // 模拟文件编辑响应
                console.log('🏷️ 编辑文件标签');
                options.onload({
                    responseText: JSON.stringify({
                        state: true,
                        message: '文件标签更新成功'
                    })
                });
            }
        }, 100 + Math.random() * 500); // 模拟网络延迟
    },
    
    // 模拟toastr通知
    toastr: {
        success: (message, title) => console.log(`✅ ${title}: ${message}`),
        error: (message, title) => console.log(`❌ ${title}: ${message}`),
        info: (message, title) => console.log(`ℹ️ ${title}: ${message}`),
        warning: (message, title) => console.log(`⚠️ ${title}: ${message}`)
    },
    
    // 模拟DOM环境
    document: {
        querySelectorAll: (selector) => {
            console.log('🔍 查询DOM元素:', selector);
            
            if (selector.includes('actors')) {
                // 模拟演员链接元素
                return [
                    {
                        textContent: '测试演员1',
                        nextElementSibling: {
                            tagName: 'STRONG',
                            classList: {
                                contains: (className) => className === 'symbol' || className === 'female'
                            }
                        }
                    },
                    {
                        textContent: '测试演员2',
                        nextElementSibling: {
                            tagName: 'STRONG',
                            classList: {
                                contains: (className) => className === 'symbol' || className === 'female'
                            }
                        }
                    },
                    {
                        textContent: '测试演员3',
                        nextElementSibling: {
                            tagName: 'STRONG',
                            classList: {
                                contains: (className) => className === 'symbol' || className === 'male'
                            }
                        }
                    }
                ];
            }
            return [];
        }
    }
};

// 将模拟环境添加到全局
Object.assign(global || window, mockEnvironment);

/**
 * 演员标签功能模块（从主脚本复制）
 */
const ActorLabels = {
    // 从页面提取演员名称
    getActorNames: function() {
        console.log('🎭 开始提取演员信息...');
        
        // 选择所有包含演员信息的链接
        const actorLinks = document.querySelectorAll('.panel-block .value a[href*="actors"]');
        const actorNames = [];
        
        // 遍历每个链接提取演员名称
        actorLinks.forEach((link) => {
            // 检查下一个兄弟元素是否为女性演员标识
            const nextSibling = link.nextElementSibling;
            if (nextSibling && nextSibling.tagName === "STRONG" && 
                nextSibling.classList.contains("symbol") && 
                nextSibling.classList.contains("female")) {
                actorNames.push(link.textContent.trim());
            }
        });
        
        console.log('🎭 提取到的演员名称:', actorNames);
        return actorNames;
    },
    
    // 获取标签ID
    getId: function(actorName) {
        return new Promise(function(resolve, reject) {
            console.log('🔍 查找演员标签ID:', actorName);
            
            GM_xmlhttpRequest({
                method: 'GET',
                url: "https://webapi.115.com/label/list?keyword=" + encodeURIComponent(actorName) + "&limit=11150",
                responseType: 'json',
                headers: {
                    "User-Agent": "Mozilla/5.0",
                    "Accept": "application/json, text/javascript, */*; q=0.01",
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    Origin: "https://115.com",
                    "X-Requested-With": "XMLHttpRequest"
                },
                onload: function(response) {
                    try {
                        var result = response.response || JSON.parse(response.responseText);
                        if (result.state && result.data && result.data.length > 0) {
                            // 查找完全匹配的标签
                            var exactMatch = result.data.find(function(item) {
                                return item.name === actorName;
                            });
                            
                            if (exactMatch) {
                                console.log('✅ 找到演员标签ID:', actorName, '->', exactMatch.id);
                                resolve(exactMatch.id);
                            } else {
                                console.log('❌ 未找到完全匹配的标签:', actorName);
                                reject(new Error('标签不存在'));
                            }
                        } else {
                            console.log('❌ 标签查询失败或无结果:', actorName);
                            reject(new Error('标签查询失败'));
                        }
                    } catch (e) {
                        console.error('❌ 解析标签查询结果失败:', e);
                        reject(e);
                    }
                },
                onerror: function(error) {
                    console.error('❌ 标签查询请求失败:', error);
                    reject(error);
                }
            });
        });
    },
    
    // 添加新标签
    addLabel: function(labelName) {
        return new Promise(function(resolve, reject) {
            console.log('➕ 创建新演员标签:', labelName);
            
            var requestBody = "name%5B%5D=" + encodeURIComponent(labelName) + "%07%23000000";
            
            GM_xmlhttpRequest({
                method: 'POST',
                url: "https://webapi.115.com/label/add_multi",
                data: requestBody,
                headers: {
                    "User-Agent": "Mozilla/5.0",
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    "Accept": "application/json, text/javascript, */*; q=0.01",
                    Origin: "https://115.com",
                    "X-Requested-With": "XMLHttpRequest"
                },
                onload: function(response) {
                    try {
                        var result = JSON.parse(response.responseText);
                        if (result.state) {
                            console.log('✅ 演员标签创建成功:', labelName);
                            resolve(result);
                        } else {
                            console.error('❌ 演员标签创建失败:', result);
                            reject(result);
                        }
                    } catch (e) {
                        console.error('❌ 解析标签创建结果失败:', e);
                        reject(e);
                    }
                },
                onerror: function(error) {
                    console.error('❌ 标签创建请求失败:', error);
                    reject(error);
                }
            });
        });
    },
    
    // 为文件添加标签
    editFile: function(fileId, labelIds) {
        return new Promise(function(resolve, reject) {
            console.log('🏷️ 为文件添加标签:', fileId, '->', labelIds);
            
            var requestBody = "fid=" + encodeURIComponent(fileId) + "&file_label=" + encodeURIComponent(labelIds);
            
            GM_xmlhttpRequest({
                method: 'POST',
                url: "https://webapi.115.com/files/edit",
                data: requestBody,
                headers: {
                    "User-Agent": "Mozilla/5.0",
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    "Accept": "application/json, text/javascript, */*; q=0.01",
                    Origin: "https://115.com",
                    "X-Requested-With": "XMLHttpRequest"
                },
                onload: function(response) {
                    try {
                        var result = JSON.parse(response.responseText);
                        if (result.state) {
                            console.log('✅ 文件标签添加成功');
                            resolve(result);
                        } else {
                            console.error('❌ 文件标签添加失败:', result);
                            reject(result);
                        }
                    } catch (e) {
                        console.error('❌ 解析文件编辑结果失败:', e);
                        reject(e);
                    }
                },
                onerror: function(error) {
                    console.error('❌ 文件编辑请求失败:', error);
                    reject(error);
                }
            });
        });
    },
    
    // 处理演员标签的完整流程
    processActorLabels: function(fileId) {
        console.log('🎬 开始处理演员标签流程...');
        
        var actorNames = this.getActorNames();
        if (actorNames.length === 0) {
            console.log('ℹ️ 未找到演员信息');
            return Promise.resolve();
        }
        
        var self = this;
        var promises = actorNames.map(function(actorName) {
            return self.getId(actorName).then(function(id) {
                console.log('✅ 获取到演员标签ID:', actorName, '->', id);
                return id;
            }).catch(function(error) {
                console.log('⚠️ 演员标签不存在，尝试创建:', actorName);
                return self.addLabel(actorName).then(function(result) {
                    console.log('✅ 演员标签创建成功:', actorName);
                    return self.getId(actorName);
                });
            });
        });
        
        return Promise.all(promises).then(function(ids) {
            var validIds = ids.filter(function(id) { return id; });
            if (validIds.length > 0) {
                var labelIds = validIds.join(',');
                console.log('🏷️ 准备添加标签到文件:', fileId, '标签IDs:', labelIds);
                
                return self.editFile(fileId, labelIds).then(function(result) {
                    console.log('🎉 演员标签处理完成!');
                    toastr.success('已为文件添加 ' + actorNames.length + ' 个演员标签', '演员标签处理完成');
                    return result;
                });
            } else {
                console.log('⚠️ 没有有效的标签ID');
                return Promise.resolve();
            }
        }).catch(function(error) {
            console.error('❌ 演员标签处理失败:', error);
            toastr.error('演员标签处理失败: ' + error.message, '处理失败');
            throw error;
        });
    }
};

/**
 * 测试函数
 */
function runActorLabelTests() {
    console.log('🎯 演员标签功能测试开始');
    console.log('=====================================');
    
    // 测试1: 演员信息提取
    console.log('\n📋 测试1: 演员信息提取');
    const actors = ActorLabels.getActorNames();
    console.log('提取结果:', actors);
    
    // 测试2: 标签查询
    console.log('\n🔍 测试2: 标签查询');
    if (actors.length > 0) {
        ActorLabels.getId(actors[0])
            .then(id => console.log('标签查询成功:', id))
            .catch(error => console.log('标签查询失败:', error.message));
    }
    
    // 测试3: 标签创建
    console.log('\n➕ 测试3: 标签创建');
    const testLabelName = '测试演员_' + Date.now();
    ActorLabels.addLabel(testLabelName)
        .then(result => console.log('标签创建成功:', result))
        .catch(error => console.log('标签创建失败:', error.message));
    
    // 测试4: 文件标签编辑
    console.log('\n🏷️ 测试4: 文件标签编辑');
    const testFileId = 'test_file_' + Date.now();
    const testLabelIds = '123,456,789';
    ActorLabels.editFile(testFileId, testLabelIds)
        .then(result => console.log('文件标签编辑成功:', result))
        .catch(error => console.log('文件标签编辑失败:', error.message));
    
    // 测试5: 完整流程
    console.log('\n🎬 测试5: 完整演员标签处理流程');
    const testFileId2 = 'complete_test_' + Date.now();
    ActorLabels.processActorLabels(testFileId2)
        .then(result => {
            console.log('✅ 完整流程测试成功');
            console.log('=====================================');
            console.log('🎉 所有测试完成!');
        })
        .catch(error => {
            console.log('❌ 完整流程测试失败:', error.message);
            console.log('=====================================');
            console.log('⚠️ 测试完成（部分失败是正常的）');
        });
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ActorLabels,
        runActorLabelTests
    };
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
    window.ActorLabelsTest = {
        ActorLabels,
        runActorLabelTests
    };
    
    // 自动运行测试
    console.log('🌐 在浏览器中运行演员标签测试...');
    runActorLabelTests();
}

// 在Node.js中运行测试
if (typeof require !== 'undefined' && require.main === module) {
    runActorLabelTests();
}
